/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { connect } from 'react-redux';
import { withTranslation } from 'react-i18next';
import Router from 'next/router';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import * as moment from 'moment';
import {
  setEditSection,
  setTheme,
  toggleTheme,
  updateProjectData,
  setIsClickedOnMenu,
  updateProjectMeta,
} from 'reducer/project';
import { logout } from 'reducer/auth';
import Modal from './notificationModal';
import Style from '../styles/leftSideBar.module.scss';
import Styles from '../styles/notification.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';

// Project Creation form component
class LeftSideBar extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      notificationStatus: false,
    };
  }

  linkButtonHandler = () => {
    const { projectPreviewData } = this.props;
    Router.push(
      '/project/share/[shareHistory]',
      `/project/share/${projectPreviewData._id}`,
    );
  };

  // Show hide notification modal
  toggleNotification = () => {
    const { notificationStatus } = this.state;
    const { updateProjectMeta, projectPreviewData } = this.props;

    // When opening the modal, mark all notifications as seen
    if (!notificationStatus) {
      const projectId = projectPreviewData?._id;
      const lastSeenKey = `notifications_last_seen_${projectId}`;
      const currentTimestamp = Date.now();

      // Store the current timestamp as last seen time
      localStorage.setItem(lastSeenKey, currentTimestamp.toString());

      // Update project meta to clear the notification flag
      updateProjectMeta({ isNewNotification: false }, projectId);

      // Update state to trigger re-render of notification count
      this.setState({
        notificationStatus: !notificationStatus,
        lastSeenTimestamp: currentTimestamp,
      });
    } else {
      this.setState({ notificationStatus: !notificationStatus });
    }
  };

  // Calculate day and hours.
  diffDaysHours = (date) => {
    const showTime = {};
    if (!date) {
      return { days: 'Unknown time', hours: 'Unknown time', numberOfDays: 0 };
    }

    const now = moment();
    const createdMoment = moment(date);

    if (!createdMoment.isValid()) {
      return { days: 'Invalid date', hours: 'Invalid date', numberOfDays: 0 };
    }

    const diffDays = now.diff(createdMoment, 'days');
    const diffHours = now.diff(createdMoment, 'hours');
    const diffMinutes = now.diff(createdMoment, 'minutes');

    if (diffDays > 0) {
      showTime.days =
        diffDays === 1 ? `${diffDays} day ago` : `${diffDays} days ago`;
    } else if (diffHours > 0) {
      showTime.days =
        diffHours === 1 ? `${diffHours} hour ago` : `${diffHours} hours ago`;
    } else if (diffMinutes > 0) {
      showTime.days =
        diffMinutes === 1
          ? `${diffMinutes} minute ago`
          : `${diffMinutes} minutes ago`;
    } else {
      showTime.days = 'Just now';
    }

    showTime.hours =
      diffHours > 0
        ? diffHours === 1
          ? `${diffHours} hour ago`
          : `${diffHours} hours ago`
        : 'Less than an hour ago';
    showTime.numberOfDays = diffDays;

    return showTime;
  };

  // Calculate the total number of new/unseen notifications
  getNotificationCount = () => {
    const { notificationsList, projectPreviewData } = this.props;

    // If there are no notifications, return 0
    if (!notificationsList || notificationsList.length === 0) {
      return 0;
    }

    // Get the last seen timestamp from localStorage
    const projectId = projectPreviewData?._id;
    const lastSeenKey = `notifications_last_seen_${projectId}`;
    const lastSeenTimestamp = localStorage.getItem(lastSeenKey);

    // Count only new activities (those added after last seen time)
    let newCount = 0;
    notificationsList.forEach((item) => {
      if (item.activities && Array.isArray(item.activities)) {
        item.activities.forEach((activity) => {
          // Only count meaningful feedback activities
          const relevantTypes = [
            'letsTalk',
            'tracking',
            'notInterested',
            'feedback',
            'comment',
            'review',
          ];
          if (!relevantTypes.includes(activity.action)) {
            return;
          }

          const activityTime =
            activity.addedAt || activity.createdAt || activity.updatedAt;
          if (activityTime) {
            const activityTimestamp = new Date(activityTime).getTime();
            const lastSeen = lastSeenTimestamp
              ? parseInt(lastSeenTimestamp)
              : 0;

            // Count as new if activity is newer than last seen time
            if (activityTimestamp > lastSeen) {
              newCount++;
            }
          }
        });
      }
    });

    return newCount > 99 ? '99+' : newCount;
  };

  // This method is used for notification modal body.
  body = () => {
    const { notificationsList } = this.props;
    return (
      <div className="mt-5 mt-sm-5 mt-md-2">
        {notificationsList === null ? (
          <div className="text-left">
            <p className="text-primary p2 pt-3">
              This is where you’ll find feedback to your project when it’s
              shared with decision makers.
            </p>
          </div>
        ) : (
          notificationsList &&
          notificationsList.map((item) => {
            return (
              <>
                <div className="col-12 d-flex flex-row p-0 text-left">
                  <div className="col-3 rounded-circle pl-0">
                    {get(item, 'decisionMakerInfo.profileImage') !== '' &&
                    get(item, 'decisionMakerInfo.profileImage') ? (
                      <img
                        src={get(item, 'decisionMakerInfo.profileImage')}
                        height="50px"
                        width="50px"
                        className="rounded-circle"
                        alt=""
                        style={{ cursor: 'pointer' }}
                      />
                    ) : (
                      <img
                        src="/assets/jpg/Placeholder_Avatar_320.jpg"
                        height="50px"
                        width="50px"
                        className="rounded-circle"
                        alt=""
                        style={{ cursor: 'pointer' }}
                      />
                    )}
                  </div>
                  <div className="col-9 p-0">
                    {item.feedback === 'letsTalk' && (
                      <p className="p2" style={{ color: '#05012D' }}>
                        {get(item, 'decisionMakerInfo.name')} wants to talk
                        about this project. You can contact them via email.{' '}
                        <a
                          href={`mailto: ${get(
                            item,
                            'decisionMakerInfo.email',
                          )}`}
                          className=""
                          style={{ color: '#1743d7' }}
                        >
                          email
                        </a>
                        .
                      </p>
                    )}
                    {item.feedback === 'tracking' && (
                      <p className="p2" style={{ color: '#05012D' }}>
                        {get(item, 'decisionMakerInfo.name')} is tracking this
                        project .
                      </p>
                    )}
                    {item.feedback === 'notInterested' && (
                      <p className="p2" style={{ color: '#05012D' }}>
                        {get(item, 'decisionMakerInfo.name')} is not interested
                        in this project at the moment.
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-12 d-flex flex-row p-0 text-left mb-3">
                  <div className="col-3 pl-0" />
                  <div className="col-9 p-0 d-flex flex-row">
                    <div
                      style={
                        item.feedback === 'letsTalk'
                          ? {
                              backgroundColor: '#00E5D5',
                              width: '12px',
                              height: '12px',
                            }
                          : item.feedback === 'tracking'
                            ? {
                                backgroundColor: '#EBFF29',
                                width: '12px',
                                height: '12px',
                              }
                            : {
                                backgroundColor: '#FF303D',
                                width: '12px',
                                height: '12px',
                              }
                      }
                      className="rounded-circle p-0"
                    />
                    <div className="col-11">
                      <p className="p3" style={{ color: '#858585' }}>
                        {this.diffDaysHours(item.updatedAt).numberOfDays !== 0
                          ? this.diffDaysHours(item.updatedAt).days
                          : this.diffDaysHours(item.updatedAt).hours}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            );
          })
        )}
      </div>
    );
  };

  render() {
    const { projectPreviewData, isNewNotification } = this.props;
    const { notificationStatus } = this.state;

    return (
      <div>
        <div className="row">
          <div className="col-8">
            {get(projectPreviewData, 'cover.title') && (
              <h2 className={`${Style.projectsTitle}`} data-cy="headerId">
                {projectPreviewData.cover.title}
              </h2>
            )}
          </div>
          <div className="col-2">
            <div className="position-relative">
              <InlineSvg
                src="/assets/svg/Bell.svg"
                height="28px"
                width="28px"
                className=""
                alt=""
                style={{ cursor: 'pointer' }}
                onClick={() => this.toggleNotification()}
              />
              {(isNewNotification || this.getNotificationCount() > 0) && (
                <div
                  className={`${Styles.notificationCount} carousel-caption text-light rounded-circle p4`}
                  onClick={() => this.toggleNotification()}
                >
                  {this.getNotificationCount()}
                </div>
              )}
            </div>
            <Modal
              modalShow={notificationStatus}
              title="Feedback"
              body={this.body()}
              closeCallback={this.toggleNotification}
              isShowCrossBtn
            />
          </div>
          <div className="col-2">
            <div className="position-relative">
              <InlineSvg
                src="/assets/svg/shareIcon.svg"
                height="28px"
                width="28px"
                className=""
                alt=""
                style={{ cursor: 'pointer' }}
                onClick={() => this.linkButtonHandler()}
              />
            </div>
          </div>
        </div>
        <p className={`${Style.registrationText} p2 mt-1`} data-cy="regNo">
          SMASH REGISTRATION #
          {get(projectPreviewData, 'regNo') && projectPreviewData.regNo}
        </p>
      </div>
    );
  }
}

LeftSideBar.propTypes = {
  projectPreviewData: PropTypes.object.isRequired,
  cover: PropTypes.object.isRequired,
  notificationsList: PropTypes.array.isRequired,
  updateProjectMeta: PropTypes.func.isRequired,
  isNewNotification: PropTypes.number.isRequired,
};

const mapStateToProps = (state) => ({
  editSection: state.project.editSection,
  userData: state.auth.userData,
  isClickedOnMenu: state.project.isClickedOnMenu,
  isNewNotification: state.project.isNewNotification,
  notificationsList: state.project.notificationsList,
});

const mapDispatchToProps = (dispatch) => {
  return {
    setEditSection: (payload) => dispatch(setEditSection(payload)),
    toggleTheme: (payload) => dispatch(toggleTheme(payload)),
    updateProjectData: (value, id) => dispatch(updateProjectData(value, id)),
    setTheme: (payload) => dispatch(setTheme(payload)),
    logout: (payload) => dispatch(logout(payload)),
    setIsClickedOnMenu: (payload) => dispatch(setIsClickedOnMenu(payload)),
    updateProjectMeta: (payload, id) =>
      dispatch(updateProjectMeta(payload, id)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation('common')(LeftSideBar));
