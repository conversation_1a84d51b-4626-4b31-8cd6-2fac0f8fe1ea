import React from 'react';
import { Field } from 'redux-form';
import { FormGroup } from 'react-bootstrap';
import { get } from 'lodash';
import style from '../../styles/collaborator.module.scss';
import RenderField from 'sharedComponents/renderfield';
import { email } from 'validation/commonValidation';

const Collaborators = ({
  projectId,
  collaboratorList,
  handleSubmit,
  submitEmailForm,
  sendReminder,
  isDeleteCollaborator,
}) => {
  console.log("collaboratorList========", collaboratorList)
  return (
    <>
      <form onSubmit={handleSubmit(submitEmailForm)} className="formStyle">
        <div className={`d-flex flex-wrap gap-3 align-items-end ${style.inviteFormRow}`}>
          <div className="flex-grow-1">
            <FormGroup name="formGroup" className="mb-0 w-100">
              <label className={`col-12 p-0 ${style.inviteInputHeading}`} style={{ textAlign: 'left' }}>
                Invite users to collaborate
              </label>
              <Field
                name="inviteEmail"
                component={RenderField}
                validate={[email]}
                type="email"
                placeholder={'Add emails here'}
                size="lg"
              />
            </FormGroup>
          </div>
          <div className="invite-button-wrapper">
            <button type="submit" id="submit_btn" className={style.inviteBtnText}>
              SEND INVITE
            </button>
          </div>
        </div>
      </form>
      {collaboratorList &&
        collaboratorList.map((item, index) => {
          const isPending = get(item, 'status') === 'pending';
          const isAccepted = get(item, 'status') === 'accepted';
          const belongsToProject = get(item, 'projectInfo.projectsId') === projectId;
          if (!belongsToProject || (!isPending && !isAccepted)) return null;
          const avatarSrc = get(item, 'profileImage') || '/assets/jpg/Placeholder_Avatar_320.jpg';
          const displayName = isPending ? get(item, 'email') : get(item, 'fullName');
          const statusText = isPending ? 'Invited' : 'Collaborating';
          return (
            <div key={String(index)} className={`d-flex align-items-center justify-content-between ${style.collaboratorRow}`}>
              <div className="d-flex align-items-center">
                <div className={`${style.collaboratorAvatar} rounded-circle me-3`}>
                  <img src={avatarSrc} height="40" width="40" className="rounded-circle" alt="avatar" />
                </div>
                <div className={style.collaboratorInfo}>
                  <span className={style.collaboratorEmail}>{displayName}</span>
                  <span className={`${style.invitedBadge} ${isPending ? style.pendingBadge : style.acceptedBadge}`}>{statusText}</span>
                </div>
              </div>
              <div className={style.collaboratorActions}>
                {isPending && (
                  <span className={`${style.actionLink} me-3`} onClick={() => sendReminder(get(item, 'email'), projectId)}>
                    Send invite reminder
                  </span>
                )}
                <span className={style.actionLink} onClick={() => isDeleteCollaborator(get(item, '_id'), displayName, get(item, 'email'), isAccepted, get(item, 'projectInfo.projectsId'))}>
                  Remove user
                </span>
              </div>
            </div>
          );
        })}
    </>
  );
};

export default Collaborators;
