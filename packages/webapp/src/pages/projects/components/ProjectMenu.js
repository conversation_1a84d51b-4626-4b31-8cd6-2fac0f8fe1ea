import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Style from '../styles/startApp.module.scss';
import Icon from 'sharedComponents/Icon/Icon';
import LockIcon from 'sharedComponents/Icon/LockIcon';
import EditIconSvg from 'svgpath/HorizontalThreeDotSvgPath';
import CrossSvg from 'svgpath/CrossIconSvgPath';
import Router from 'next/router';
import { get, isEmpty } from 'lodash';
import CollaboratorsModalBody from './modals/CollaboratorsModalBody';
import Modal from 'sharedComponents/Modal/modal';
import InviteCollaborators from './modals/InviteCollaborators';
import { reduxForm, reset } from 'redux-form';
import style from '../styles/collaborator.module.scss';
import { useDispatch, useSelector } from 'react-redux';
import { setSubscriptionJourneyModal } from 'reducer/subscription';
import { createCollaborator, deleteCollaborator, fetchCollaborator, fetchCollaboratorList, removeSectionItems, sendReminderToCollaborator, softDeleteProject } from 'reducer/project';

/**
 * ProjectMenu is a unified menu for both project owner and collaborator actions.
 * Use the boolean props to control which menu options are shown.
 */
const ProjectMenu = ({
  item,
  isCollaboratingProject = false,
  isCollaborationLocked,
  isOpen = false,
  onMenuToggle = () => { },
  actions = [],
  collaboratorList,
  isLocked
}) => {
  // Local state for modals and collaborator actions
  const [showCollaboratorsModal, setShowCollaboratorsModal] = useState(false);
  const [showCollaboratorsListModal, setShowCollaboratorsListModal] = useState(false); // <-- New state for collaborators list modal
  const [isShowCollaboratorsDelete, setIsShowCollaboratorsDelete] = useState(false);
  const [collaboratorToDelete, setCollaboratorToDelete] = useState(null);
  const [collaboratorToStop, setCollaboratorToStop] = useState(null);
  const [collaboratorName, setCollaboratorName] = useState('');
  const [isShowStopCollaborating, setIsShowStopCollaborating] = useState(false)
  const [showDeleteProjectModal, setShowDeleteProjectModal] = useState(false); // <-- Added state for delete project modal

  const dispatch = useDispatch();
  const userData = useSelector((state) => state.auth.userData);

  // Handler functions
  const handleEdit = () => {
    Router.push('/project/overview/[homePage]', `/project/overview/${item._id}`);
  };

  const handleCollaborators = (e) => {
    e.stopPropagation();
    if (isCollaborationLocked) {
      dispatch(setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'projects',
        onComplete: handleCollaborators,
        feature: 'unlimitedProjects',
      }));
    } else {
      if (dispatch) dispatch(fetchCollaboratorList(item._id));
      if (dispatch) dispatch(fetchCollaborator({
        email: encodeURIComponent(item.email),
        id: item._id,
      }));
      // If in the collaborators section, open the list modal, else open invite modal
      if (isCollaboratingProject) {
        setShowCollaboratorsListModal(true);
      } else {
        setShowCollaboratorsModal(true);
      }
    }
  };

  const handleStopCollaborating = (collaborator) => {
    setIsShowStopCollaborating(true);
    if (!isEmpty(collaborator.projectCollaborator)) {
      const matchedCollaborator = collaborator.projectCollaborator.find(
        (collab) => collab.id === userData._id
      );
      const matchedId = matchedCollaborator ? matchedCollaborator._id : null;
      setCollaboratorToStop(matchedId);
    }
  };

  const handleDelete = () => {
    setShowDeleteProjectModal(true);
  };


  const handleCloseCollaboratorsModal = () => {
    setShowCollaboratorsModal(false);
  };
  const handleCloseCollaboratorsListModal = () => {
    setShowCollaboratorsListModal(false);
  };

  const handleCloseModal = () => {
    setShowCollaboratorsListModal(false);
    setShowCollaboratorsModal(false);
  };
  const closeDeleteCollaboratorModal = () => {
    setIsShowCollaboratorsDelete(false);
    setCollaboratorToDelete(null);
    setCollaboratorName('');
  };
  const closeStopCollaboratingModal = () => {
    setIsShowStopCollaborating(false)
    setCollaboratorToDelete(null);
    setCollaboratorName('');
  };
  const closeDeleteProjectModal = () => {
    setShowDeleteProjectModal(false);
  };

  const handleToggleEdit = (e) => {
    e.stopPropagation();
    if (isLocked) {
      dispatch(setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'projects',
        onComplete: handleToggleEdit,
        feature: 'unlimitedProjects',
      }));
      return;
    }
    onMenuToggle();
  };
  const handleSubmitEmailForm = async (values, dispatch) => {
    const name = get(item, 'creator.username', '');
    const projectId = get(item, '_id');
    const title = get(item, 'cover.title');
    const producerName = get(item, 'cover.producer');
    const directorName = get(item, 'cover.director');
    const writerName = get(item, 'cover.writer');
    const creatorId = get(item, 'creator.userId');

    if (projectId) {
      const value = {
        projectCreatorInfo: {
          projectCreatorId: creatorId,
          fullName: name,
        },
        projectInfo: {
          projectsId: projectId,
          title: title,
          producer: producerName,
          director: directorName,
          writer: writerName,
        },
        status: 'pending',
        email: values.inviteEmail,
        profileImage: null,
        fullName: null,
        reminders: [
          {
            date: new Date().toJSON().slice(0, 10).replace(/-/g, '/'),
          },
        ],
      };

      try {
        // Create collaborator and wait for it to complete
        await dispatch(createCollaborator(value));

        // Fetch updated collaborator list after successful creation
        await dispatch(fetchCollaboratorList(projectId));

        // Reset the form after successful submission
        dispatch(reset('inviteCollaboratorsForm'));

      } catch (error) {
        console.error('Error creating collaborator:', error);
      }
    }
  };

  const handleSendReminder = async (email, projectId) => {
    let collaboratorData = null;
    if (email && projectId && dispatch) {
      collaboratorData = await dispatch(fetchCollaborator({
        email: encodeURIComponent(email),
        id: projectId,
      }));
      const data = collaboratorData ? collaboratorData[0] : false;
      const id = get(data, '_id');
      const oldDates = get(data, 'reminders', []);
      const reninderNewDate = {
        date: new Date().toJSON().slice(0, 10).replace(/-/g, '/'),
      };
      oldDates.push(reninderNewDate);
      if (data) {
        const value = {
          projectCreatorInfo: {
            projectCreatorId: get(data, 'projectCreatorInfo.projectCreatorId'),
            fullName: get(data, 'projectCreatorInfo.fullName', ''),
          },
          projectInfo: {
            projectsId: projectId,
            title: get(data, 'projectInfo.title'),
            producer: get(data, 'projectInfo.producer'),
            director: get(data, 'projectInfo.director'),
            writer: get(data, 'projectInfo.writer'),
          },
          status: 'pending',
          email: get(data, 'email'),
          profileImage: null,
          fullName: null,
          reminders: oldDates,
        };
        if (dispatch) dispatch(sendReminderToCollaborator(value, id));
      }
    }
  };

  const handleDeleteCollaborator = (id, name, email, isAccepted, projectId) => {
    setIsShowCollaboratorsDelete(true);
    setCollaboratorToDelete({ _id: id, fullName: name, email, isAccepted, projectId });
    setCollaboratorName(name);
  };

  const owner = item.creator || {};
  const collaborators = (collaboratorList || []).filter(
    (collab) => (collab.email !== owner.email && collab.status !== "pending")
  );

  const collaboratorsBody = (
    <CollaboratorsModalBody owner={owner} collaborators={collaborators} />
  );

  return (
    <div className={Style.iconPosition} onClick={handleToggleEdit}>
      {isLocked ? (
        <LockIcon height={24} width={24} show={true} viewbox="0 0 24 24" />
      ) : !isOpen ? (
        <Icon
          icon={EditIconSvg}
          color="#00000"
          viewBox="32"
          iconSize="16px"
        />
      ) : (
        <Icon icon={CrossSvg} color="#00000" viewBox="32" iconSize="26px" />
      )}
      {isOpen && (
        <div className={Style.dropdownPos} style={isCollaborationLocked ? { minWidth: 180 } : {}}>
          <div className={`${Style.dropDownContent} p-0`} style={isCollaborationLocked ? { minWidth: 180, boxSizing: 'border-box' } : {}}>
            {actions.map((action) => {
              if (action === 'EDIT') {
                return (
                  <div key="edit" onClick={handleEdit}>
                    <p className={`${Style.collaboratorDropDownOptions || Style.dropDownOptions} p2 text-primary mb-0 mt-12`} style={{ whiteSpace: 'nowrap' }}>
                      EDIT
                    </p>
                  </div>
                );
              }
              if (action === 'COLLABORATORS') {
                return (
                  <div key="collaborators" onClick={handleCollaborators} style={{ cursor: 'pointer' }}>
                    <p
                      className={`${Style.collaboratorDropDownOptions || Style.dropDownOptions} p2 text-primary`}
                      style={
                        isCollaborationLocked
                          ? { display: 'flex', alignItems: 'center', whiteSpace: 'nowrap', width: '100%' }
                          : { whiteSpace: 'nowrap' }
                      }
                    >
                      COLLABORATORS
                      {isCollaborationLocked && (
                        <span style={{ marginLeft: 8, display: 'flex', alignItems: 'center' }}>
                          <LockIcon height={17} width={16} show={true} />
                        </span>
                      )}
                    </p>
                  </div>
                );
              }
              if (action === 'STOP COLLABORATING') {
                return (
                  <div key="stop-collaborating" onClick={() => handleStopCollaborating(item, "stopCollaboraing")}>
                    <p className={`${Style.collaboratorDropDownOptions || Style.dropDownOptions} p2 text-primary mb-12`}>
                      STOP COLLABORATING
                    </p>
                  </div>
                );
              }
              if (action === 'DELETE') {
                return (
                  <div key="delete" onClick={handleDelete}>
                    <p className={`${Style.collaboratorDropDownOptions || Style.dropDownOptions} p2 text-primary mb-12`}>
                      DELETE
                    </p>
                  </div>
                );
              }
              return null;
            })}
          </div>
        </div>
      )}
      {/* Delete Project Modal - as in ExisitingProject.js */}
      <Modal
        modalShow={showDeleteProjectModal}
        title="Are you sure you want to delete this project?"
        body="It will be relocated to the Recently Removed Projects section for 30 days before being permanently removed."
        closeCallback={closeDeleteProjectModal}
        closeBtnText="CANCEL"
        successCallback={() => { if (dispatch) dispatch(softDeleteProject(item._id)); closeDeleteProjectModal(); }}
        successBtnText="DELETE"
        titleClass={`${Style.modalTitle} m-0`}
        modalSize="custom"
        closeBtnClass="cancelButton"
      />
      <Modal
        // modalShow={modalOpenId === item._id}
        title="Collaborators"
        body={collaboratorsBody}
        closeCallback={handleCloseModal}
        titleClass={`${Style.modalTitle}`}
        modalSize="xl"
        className={style.inviteModal}
      // Add any additional modal props/styles as needed
      />
      {/* Collaborators Modal - as in ExisitingProject.js, with all functionalities */}
      <Modal
        modalShow={showCollaboratorsModal}
        title="Collaborators"
        body={
          <InviteCollaboratorsForm
            projectId={item._id}
            collaboratorList={collaboratorList}
            submitEmailForm={handleSubmitEmailForm}
            sendReminder={handleSendReminder}
            isDeleteCollaborator={handleDeleteCollaborator}
          />
        }
        closeCallback={handleCloseCollaboratorsModal}
        titleClass={`${Style.modalTitle}`}
        modalSize="xl"
        className={Style.inviteModal}
        modalHeader={Style.modalHeader}
        svgIconClass={Style.svgIconClass}
        modalFooter={Style.modalFooter}
      />
      {/* Collaborators List Modal - New modal for viewing collaborators list */}
      <Modal
        modalShow={showCollaboratorsListModal}
        title="Collaborators"
        body={collaboratorsBody}
        closeCallback={handleCloseCollaboratorsListModal}
        titleClass={`${Style.modalTitle}`}
        modalSize="xl"
        className={Style.inviteModal}
      />
      {/* Delete Collaborator Modal - as in ExisitingProject.js */}
      <Modal
        modalShow={isShowCollaboratorsDelete}
        title="Remove collaborator?"
        body={`Are you sure you want to remove ${collaboratorName} as a collaborator? They won’t be able to access this project anymore.`}
        closeCallback={closeDeleteCollaboratorModal}
        closeBtnText="CANCEL"
        successCallback={async () => {
          closeDeleteCollaboratorModal();
          // if (collaboratorToDelete.isAccepted) {
          // if (dispatch) await dispatch(removeSectionItems(
          //   collaboratorToDelete.projectId,
          //   'projectCollaborator',
          //   collaboratorToDelete._id,
          // ));
          // } else {
          if (dispatch) await dispatch(deleteCollaborator(collaboratorToDelete?._id));
          // }
          if (dispatch) await dispatch(fetchCollaboratorList(item._id));

        }}
        successBtnText="DELETE"
        titleClass={`${Style.modalTitle}`}
        closeBtnClass="cancelButton"
        className={Style.inviteModal}
        svgIconClass={Style.svgIconClass}
        modalSize="xl"
        modalFooter={Style.footerRightAlign}
        bodyClass={Style.bodyClass}
      />
      <Modal
        modalShow={isShowStopCollaborating}
        title="Stop collaborating?"
        body={`Are you sure you want to stop as a collaborator? You won’t be able to access this project anymore.`}
        closeCallback={closeStopCollaboratingModal}
        closeBtnText="CANCEL"
        successCallback={async () => {
          if (dispatch) await dispatch(removeSectionItems(
            item._id,
            'projectCollaborator',
            collaboratorToStop,
          ));
          closeStopCollaboratingModal();
          // if (dispatch) await dispatch(deleteCollaborator(userData?._id));
          if (dispatch) await dispatch(fetchCollaboratorList(item._id));

        }}
        successBtnText="Stop Collaborating"
        titleClass={`${Style.modalTitle}`}
        closeBtnClass="cancelButton"
        className={Style.inviteModal}
        svgIconClass={Style.svgIconClass}
        modalSize="xl"
        modalFooter={Style.footerRightAlign}
        bodyClass={Style.bodyClass}
      />
    </div>
  );
};

const InviteCollaboratorsForm = reduxForm({ form: 'inviteCollaboratorsForm' })(InviteCollaborators);

ProjectMenu.propTypes = {
  item: PropTypes.object.isRequired,
  accessibleFeature: PropTypes.object.isRequired,
  showCollaboratorsOption: PropTypes.bool,
  showStopCollaboratingOption: PropTypes.bool,
  showDeleteOption: PropTypes.bool,
  isCollaborationLocked: PropTypes.bool,
  fetchCollaboratorList: PropTypes.func,
};

export default ProjectMenu;
